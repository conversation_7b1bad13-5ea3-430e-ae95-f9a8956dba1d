'use client';

import { DashboardHome } from '@/components/organisms/DashboardHome/DashboardHome';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { EUserRole } from '@/config/enums/user';
import { ERoutes } from '@/config/enums/enum';

export default function HomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Redirect independent teachers to the Manage Worksheet page
    if (status === 'authenticated' && session?.user?.role === EUserRole.INDEPENDENT_TEACHER) {
      router.push(ERoutes.MANAGE_WORKSHEET);
    }
  }, [session, status, router]);

  // For other roles, show the regular dashboard
  return <DashboardHome />;
}
